# نظام المحاسبة الشامل

برنامج محاسبي متكامل باللغة العربية يدعم إدارة عدة أقسام مع تتبع الإيرادات والمصروفات بشكل يومي وشهري.

## المميزات

### 🏢 إدارة الأقسام
- **5 أقسام افتراضية**: بلايستيشن، شبكة، استديو كانون، استديو غزة، قسم إضافي
- إمكانية تغيير أسماء الأقسام من الإعدادات
- عرض منفصل لكل قسم مع إحصائياته الخاصة

### 💰 تتبع المالي
- **الإيرادات**: إضافة وتتبع إيرادات كل قسم
- **المصروفات**: تصنيف المصروفات إلى:
  - كهرباء
  - إيجار  
  - مصروفات أخرى
- **حساب تلقائي**: خصم المصروفات من الإيرادات لحساب صافي الربح

### 📊 التقارير
- **التقرير الشامل**: عرض إجمالي جميع الأقسام
- **التقارير اليومية**: تقارير مفصلة لكل يوم
- **التقارير الشهرية**: إحصائيات شهرية شاملة
- **فلترة**: إمكانية عرض تقارير قسم واحد أو جميع الأقسام

### 🖨️ الطباعة
- اكتشاف الطابعات المتصلة بالكمبيوتر
- طباعة التقارير مباشرة
- دعم طابعات متعددة

### ⚙️ الإعدادات
- **العملات المدعومة**:
  - ريال يمني
  - ريال سعودي
  - دولار أمريكي
- تخصيص أسماء الأقسام
- واجهة عربية كاملة

### 🕐 التحديث التلقائي
- تحديث التاريخ تلقائياً مع تاريخ الكمبيوتر
- حفظ البيانات في قاعدة بيانات محلية
- واجهة عصرية وسريعة الاستجابة

## متطلبات التشغيل

- Python 3.7 أو أحدث
- نظام التشغيل Windows (للطباعة)
- متصفح ويب حديث

## التثبيت والتشغيل

### 1. تثبيت Python
تأكد من تثبيت Python على جهازك من [python.org](https://python.org)

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python app.py
```

### 4. فتح البرنامج
سيفتح البرنامج تلقائياً في المتصفح على العنوان:
```
http://127.0.0.1:5000
```

## طريقة الاستخدام

### إضافة إيراد أو مصروف
1. اختر القسم المطلوب
2. اضغط على "إضافة إيراد" أو "إضافة مصروف"
3. أدخل المبلغ والوصف
4. اختر نوع المصروف (إذا كان مصروفاً)
5. اضغط "إضافة"

### عرض التقارير
1. اضغط على "التقرير الشامل" لعرض إجمالي جميع الأقسام
2. اضغط على "التقارير اليومية" واختر التاريخ
3. اضغط على "التقارير الشهرية" واختر الشهر

### تغيير الإعدادات
1. اضغط على "الإعدادات"
2. اختر العملة المطلوبة
3. غيّر أسماء الأقسام حسب الحاجة
4. اضغط "حفظ الإعدادات"

### الطباعة
1. اضغط على زر "طباعة"
2. اختر الطابعة المطلوبة
3. اضغط "طباعة"

## هيكل المشروع

```
برنامج محاسبي/
├── app.py                 # الملف الرئيسي للتطبيق
├── requirements.txt       # متطلبات Python
├── README.md             # دليل الاستخدام
├── accounting_system.db  # قاعدة البيانات (تُنشأ تلقائياً)
├── templates/
│   └── index.html        # واجهة المستخدم
└── static/
    ├── css/
    │   └── style.css     # ملف التصميم
    └── js/
        └── app.js        # ملف JavaScript
```

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite محلية تحتوي على:

- **departments**: جدول الأقسام
- **transactions**: جدول المعاملات المالية  
- **settings**: جدول الإعدادات

## الدعم الفني

للمساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص

هذا البرنامج مطور خصيصاً لاحتياجاتك ومتاح للاستخدام الشخصي والتجاري.

---

**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2024  
**الإصدار**: 1.0
