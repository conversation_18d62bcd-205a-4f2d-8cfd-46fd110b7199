<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام المحاسبة</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        .login-container { 
            background: white; 
            padding: 40px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            width: 100%; 
            max-width: 400px; 
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .login-container h1 { 
            margin-bottom: 30px; 
            color: #2d3748; 
            font-size: 1.8rem; 
            font-weight: 700;
        }
        .login-form .form-group { 
            margin-bottom: 20px; 
            text-align: right; 
        }
        .login-form label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600; 
            color: #4a5568; 
        }
        .login-form input { 
            width: 100%; 
            padding: 12px 15px; 
            border: 1px solid #cbd5e0; 
            border-radius: 8px; 
            font-family: 'Cairo', sans-serif; 
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .login-form input:focus {
            border-color: #4a55e1;
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 85, 225, 0.1);
        }
        .login-btn { 
            width: 100%; 
            padding: 12px; 
            background: linear-gradient(135deg, #4a55e1, #667eea);
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 1.1rem; 
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        .login-btn:hover { 
            background: linear-gradient(135deg, #3c46b5, #5a67d8);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 85, 225, 0.3);
        }
        .flash-message { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 8px; 
            color: #fff; 
            font-weight: 600;
        }
        .flash-message.error { background: linear-gradient(135deg, #e53e3e, #fc8181); }
        .flash-message.success { background: linear-gradient(135deg, #38a169, #68d391); }
        .flash-message.info { background: linear-gradient(135deg, #3182ce, #63b3ed); }
        .login-info {
            margin-top: 25px;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
            border-right: 4px solid #4a55e1;
        }
        .login-info h3 {
            margin-bottom: 10px;
            color: #2d3748;
            font-size: 1rem;
        }
        .login-info p {
            margin: 5px 0;
            color: #718096;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🖥️ نظام المحاسبة الشامل</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" action="{{ url_for('login') }}" class="login-form">
            <div class="form-group">
                <label for="username">👤 اسم المستخدم</label>
                <input type="text" id="username" name="username" required placeholder="أدخل اسم المستخدم">
            </div>
            <div class="form-group">
                <label for="password">🔒 كلمة المرور</label>
                <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
            </div>
            <button type="submit" class="login-btn">تسجيل الدخول</button>
        </form>

        <div class="login-info">
            <h3>💡 معلومات الدخول الافتراضية:</h3>
            <p><strong>اسم المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin</p>
            <p><small>سيتم طلب تغيير كلمة المرور عند أول دخول</small></p>
        </div>
    </div>
</body>
</html>