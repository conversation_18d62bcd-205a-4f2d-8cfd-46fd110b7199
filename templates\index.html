<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة الشامل</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-calculator"></i> نظام المحاسبة الشامل</h1>
                <div class="header-info">
                    <span id="current-date"></span>
                    <span id="current-currency">ريال يمني</span>
                </div>
            </div>
        </header>

        <nav class="navbar">
            <div class="nav-buttons">
                <button class="nav-btn active" data-section="departments"><i class="fas fa-building"></i> الأقسام</button>
                <button class="nav-btn" data-section="reports"><i class="fas fa-chart-line"></i> التقرير الشامل</button>
                <button class="nav-btn" data-section="daily-reports"><i class="fas fa-calendar-day"></i> التقارير اليومية</button>
                <button class="nav-btn" data-section="monthly-reports"><i class="fas fa-calendar-alt"></i> التقارير الشهرية</button>
                <button class="nav-btn" data-section="settings"><i class="fas fa-cog"></i> الإعدادات</button>
                <button class="nav-btn print-btn" onclick="printReport()"><i class="fas fa-print"></i> طباعة</button>
                <button class="nav-btn exit-btn" onclick="exitApplication()"><i class="fas fa-sign-out-alt"></i> خروج</button>
            </div>
        </nav>

        <section id="departments-section" class="section active">
            <div class="departments-grid" id="departments-grid"></div>
        </section>

        <section id="reports-section" class="section">
            <div class="reports-container">
                <h2><i class="fas fa-chart-pie"></i> التقرير الشامل (لليوم الحالي)</h2>
                <div class="summary-cards">
                    <div class="summary-card total-income"><h3>إجمالي الإيرادات</h3><div class="amount" id="total-income">0</div></div>
                    <div class="summary-card total-expenses"><h3>إجمالي المصروفات</h3><div class="amount" id="total-expenses">0</div></div>
                    <div class="summary-card net-profit"><h3>صافي الربح</h3><div class="amount" id="net-profit">0</div></div>
                </div>
                <div class="departments-summary" id="departments-summary"></div>
            </div>
        </section>

        <section id="daily-reports-section" class="section">
            <div class="reports-container">
                <h2><i class="fas fa-calendar-day"></i> التقارير اليومية</h2>
                <div class="date-filter">
                    <label for="daily-date">اختر التاريخ:</label>
                    <input type="date" id="daily-date">
                    <button onclick="loadDailyReport()">عرض التقرير</button>
                </div>
                <div id="daily-report-content"></div>
            </div>
        </section>

        <section id="monthly-reports-section" class="section">
            <div class="reports-container">
                <h2><i class="fas fa-calendar-alt"></i> التقارير الشهرية</h2>
                <div class="month-filter">
                    <label for="monthly-date">اختر الشهر:</label>
                    <input type="month" id="monthly-date">
                    <button onclick="loadMonthlyReport()">عرض التقرير</button>
                </div>
                <div id="monthly-report-content"></div>
            </div>
        </section>

        <section id="settings-section" class="section">
            <div class="settings-container">
                <h2><i class="fas fa-cog"></i> الإعدادات</h2>
                <div class="settings-group">
                    <h3><i class="fas fa-plus-circle"></i> إضافة قسم جديد</h3>
                    <div class="add-department-form">
                        <input type="text" id="new-department-name" placeholder="أدخل اسم القسم الجديد">
                        <button onclick="addNewDepartment()" class="add-btn">إضافة</button>
                    </div>
                </div>
                <div class="settings-group">
                    <h3><i class="fas fa-edit"></i> تعديل أسماء الأقسام الحالية</h3>
                    <div id="department-names-settings"></div>
                </div>
                <div class="settings-group">
                    <h3><i class="fas fa-money-bill-wave"></i> إعدادات العملة</h3>
                    <select id="currency-select">
                        <option value="ريال يمني">ريال يمني</option>
                        <option value="ريال سعودي">ريال سعودي</option>
                        <option value="دولار أمريكي">دولار أمريكي</option>
                    </select>
                </div>
                <button class="save-settings-btn" onclick="saveSettings()"><i class="fas fa-save"></i> حفظ جميع الإعدادات</button>
            </div>
        </section>
    </div>

    <div id="transaction-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeTransactionModal()">&times;</span>
            <h3 id="modal-title">إضافة معاملة</h3>
            <form id="transaction-form">
                <input type="hidden" id="department-id"><input type="hidden" id="transaction-type">
                <div class="form-group"><label for="amount">المبلغ:</label><input type="number" id="amount" step="0.01" required></div>
                <div class="form-group" id="expense-type-group" style="display: none;">
                    <label for="expense-type">نوع المصروف:</label>
                    <select id="expense-type">
                        <option value="electricity">كهرباء</option><option value="rent">إيجار</option><option value="other">مصروفات أخرى</option>
                    </select>
                </div>
                <div class="form-group"><label for="description">الوصف (اختياري):</label><input type="text" id="description"></div>
                <div class="form-buttons"><button type="submit">إضافة</button><button type="button" onclick="closeTransactionModal()">إلغاء</button></div>
            </form>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>