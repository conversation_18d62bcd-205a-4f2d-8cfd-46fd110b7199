<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تغيير كلمة المرور</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body { 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        .login-container { 
            background: white; 
            padding: 40px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            width: 100%; 
            max-width: 450px; 
            text-align: center;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .login-container h1 { 
            margin-bottom: 30px; 
            color: #2d3748; 
            font-size: 1.8rem; 
            font-weight: 700;
        }
        .login-form .form-group { 
            margin-bottom: 20px; 
            text-align: right; 
        }
        .login-form label { 
            display: block; 
            margin-bottom: 8px; 
            font-weight: 600; 
            color: #4a5568; 
        }
        .login-form input { 
            width: 100%; 
            padding: 12px 15px; 
            border: 1px solid #cbd5e0; 
            border-radius: 8px; 
            font-family: 'Cairo', sans-serif; 
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .login-form input:focus {
            border-color: #4a55e1;
            outline: none;
            box-shadow: 0 0 0 3px rgba(74, 85, 225, 0.1);
        }
        .login-btn { 
            width: 100%; 
            padding: 12px; 
            background: linear-gradient(135deg, #38a169, #48bb78);
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            font-size: 1.1rem; 
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        .login-btn:hover { 
            background: linear-gradient(135deg, #2f855a, #38a169);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(56, 161, 105, 0.3);
        }
        .flash-message { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 8px; 
            color: #fff; 
            font-weight: 600;
        }
        .flash-message.error { background: linear-gradient(135deg, #e53e3e, #fc8181); }
        .flash-message.info { background: linear-gradient(135deg, #3182ce, #63b3ed); }
        .password-requirements {
            margin-top: 20px;
            padding: 15px;
            background: #f0fff4;
            border-radius: 8px;
            border-right: 4px solid #38a169;
            text-align: right;
        }
        .password-requirements h3 {
            margin-bottom: 10px;
            color: #2d3748;
            font-size: 1rem;
        }
        .password-requirements ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .password-requirements li {
            margin: 8px 0;
            color: #718096;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .password-requirements li::before {
            content: "•";
            color: #38a169;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🔐 تغيير كلمة المرور</h1>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <form method="POST" action="{{ url_for('change_password') }}" class="login-form">
            <div class="form-group">
                <label for="new_password">كلمة المرور الجديدة</label>
                <input type="password" id="new_password" name="new_password" required placeholder="أدخل كلمة المرور الجديدة">
            </div>
            <div class="form-group">
                <label for="confirm_password">تأكيد كلمة المرور</label>
                <input type="password" id="confirm_password" name="confirm_password" required placeholder="أعد إدخال كلمة المرور الجديدة">
            </div>
            <button type="submit" class="login-btn">حفظ وتغيير</button>
        </form>

        <div class="password-requirements">
            <h3>📋 متطلبات كلمة المرور:</h3>
            <ul>
                <li>يجب أن تكون كلمة المرور مكونة من 4 أحرف على الأقل</li>
                <li>يجب أن تتطابق كلمتا المرور في الحقلين</li>
                <li>تأكد من استخدام كلمة مرور قوية وسهلة التذكر</li>
            </ul>
        </div>
    </div>
</body>
</html>