#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app, init_database
    import threading
    import webbrowser
    import time
    
    def open_browser():
        """فتح المتصفح تلقائياً"""
        time.sleep(2)
        webbrowser.open('http://127.0.0.1:5000')
    
    if __name__ == '__main__':
        print("Starting Accounting System...")
        
        # تهيئة قاعدة البيانات
        print("Initializing database...")
        init_database()
        print("Database initialized successfully")
        
        # فتح المتصفح في خيط منفصل
        print("Opening browser...")
        threading.Thread(target=open_browser, daemon=True).start()
        
        # تشغيل التطبيق
        print("Starting Flask application...")
        print("Access the application at: http://127.0.0.1:5000")
        
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)

except Exception as e:
    print(f"Error starting application: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
