#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج محاسبي شامل
نظام محاسبة متكامل للأقسام المتعددة
"""

import os
import sqlite3
import threading
import time
import webbrowser
from datetime import date, datetime
from flask import Flask, jsonify, render_template, request, redirect, url_for, session, flash
from werkzeug.security import generate_password_hash, check_password_hash

app = Flask(__name__)
app.config['SECRET_KEY'] = 'accounting_system_2024_secret_key'

# إعدادات قاعدة البيانات
DATABASE = 'accounting_system.db'

def get_db_connection():
    """إنشاء اتصال بقاعدة البيانات"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    conn = get_db_connection()
    # جدول الأقسام (مع إضافة قيد UNIQUE على الاسم لمنع التكرار)
    conn.execute('''
        CREATE TABLE IF NOT EXISTS departments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            display_order INTEGER DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    # جدول المعاملات
    conn.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            department_id INTEGER NOT NULL,
            transaction_type TEXT NOT NULL,
            expense_type TEXT,
            amount DECIMAL(10,2) NOT NULL,
            description TEXT,
            transaction_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (department_id) REFERENCES departments (id)
        )
    ''')
    # جدول الإعدادات
    conn.execute('''
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT NOT NULL,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    # جدول المستخدمين
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            must_change_password BOOLEAN DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    default_departments = [('بلايستيشن', 1), ('شبكة', 2), ('استديو كانون', 3), ('استديو غزة', 4), ('قسم إضافي', 5)]
    for dept_name, order in default_departments:
        conn.execute('INSERT OR IGNORE INTO departments (name, display_order) VALUES (?, ?)', (dept_name, order))
    
    default_settings = [('currency', 'ريال يمني'), ('company_name', 'نظام المحاسبة'), ('date_format', 'dd/mm/yyyy')]
    for key, value in default_settings:
        conn.execute('INSERT OR IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)', (key, value))
    
    # إضافة المستخدم الافتراضي إذا لم يكن موجوداً
    default_password_hash = generate_password_hash('admin')
    conn.execute('INSERT OR IGNORE INTO users (username, password_hash, must_change_password) VALUES (?, ?, ?)', 
                 ('admin', default_password_hash, 1))
    
    conn.commit()
    conn.close()

# دالة للتحقق من تسجيل الدخول
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# دالة للتحقق من ضرورة تغيير كلمة المرور
def password_change_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' in session:
            conn = get_db_connection()
            user = conn.execute('SELECT must_change_password FROM users WHERE id = ?', (session['user_id'],)).fetchone()
            conn.close()
            if user and user['must_change_password']:
                return redirect(url_for('change_password'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

@app.route('/login', methods=['GET', 'POST'])
def login():
    # إذا كان المستخدم مسجل دخول بالفعل، توجيه إلى الصفحة الرئيسية
    if 'user_id' in session:
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user = conn.execute('SELECT * FROM users WHERE username = ? AND is_active = 1', (username,)).fetchone()
        conn.close()
        
        if user and check_password_hash(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            
            # إذا كان يجب تغيير كلمة المرور، توجيه إلى صفحة التغيير
            if user['must_change_password']:
                return redirect(url_for('change_password'))
            
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']
        
        if not new_password or len(new_password) < 4:
            flash('كلمة المرور يجب أن تكون على الأقل 4 أحرف', 'error')
        elif new_password != confirm_password:
            flash('كلمات المرور غير متطابقة', 'error')
        else:
            # تحديث كلمة المرور
            conn = get_db_connection()
            new_password_hash = generate_password_hash(new_password)
            conn.execute('UPDATE users SET password_hash = ?, must_change_password = 0 WHERE id = ?', 
                        (new_password_hash, session['user_id']))
            conn.commit()
            conn.close()
            
            flash('تم تغيير كلمة المرور بنجاح', 'success')
            return redirect(url_for('index'))
    
    return render_template('change_password.html')

@app.route('/logout')
@login_required
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/')
@login_required
@password_change_required
def index():
    return render_template('index.html')

# --- API لإضافة قسم جديد ---
@app.route('/api/department', methods=['POST'])
@login_required
def add_department():
    data = request.get_json()
    name = data.get('name')
    if not name or not name.strip():
        return jsonify({'success': False, 'message': 'اسم القسم مطلوب'}), 400
    
    conn = get_db_connection()
    try:
        max_order_cursor = conn.execute('SELECT MAX(display_order) as max_o FROM departments').fetchone()
        next_order = (max_order_cursor['max_o'] or 0) + 1
        
        cursor = conn.execute('INSERT INTO departments (name, display_order) VALUES (?, ?)', (name.strip(), next_order))
        conn.commit()
        new_dept_id = cursor.lastrowid
        conn.close()
        return jsonify({'success': True, 'message': 'تم إضافة القسم بنجاح', 'department': {'id': new_dept_id, 'name': name, 'display_order': next_order}})
    except sqlite3.IntegrityError:
        conn.close()
        return jsonify({'success': False, 'message': 'هذا القسم موجود بالفعل'}), 409

@app.route('/api/departments')
@login_required
def get_departments():
    conn = get_db_connection()
    departments = conn.execute('SELECT * FROM departments WHERE is_active = 1 ORDER BY display_order').fetchall()
    conn.close()
    return jsonify([dict(dept) for dept in departments])

@app.route('/api/settings', methods=['GET', 'POST'])
@login_required
def handle_settings():
    conn = get_db_connection()
    if request.method == 'POST':
        data = request.get_json()
        for key, value in data.items():
            conn.execute('INSERT OR REPLACE INTO settings (setting_key, setting_value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)', (key, value))
        conn.commit()
        conn.close()
        return jsonify({'success': True, 'message': 'تم حفظ الإعدادات بنجاح'})
    else:
        settings = conn.execute('SELECT * FROM settings').fetchall()
        conn.close()
        return jsonify({setting['setting_key']: setting['setting_value'] for setting in settings})

@app.route('/api/transactions', methods=['POST'])
@login_required
def add_transaction():
    data = request.get_json()
    conn = get_db_connection()
    conn.execute(
        'INSERT INTO transactions (department_id, transaction_type, expense_type, amount, description, transaction_date) VALUES (?, ?, ?, ?, ?, ?)',
        (data['department_id'], data['transaction_type'], data.get('expense_type'), data['amount'], data.get('description', ''), data.get('transaction_date', date.today().isoformat()))
    )
    conn.commit()
    conn.close()
    return jsonify({'success': True, 'message': 'تم إضافة المعاملة بنجاح'})

@app.route('/api/daily_summary/<int:department_id>')
@login_required
def get_daily_summary(department_id):
    today = date.today().isoformat()
    conn = get_db_connection()
    income = conn.execute("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE department_id = ? AND transaction_type = 'income' AND transaction_date = ?", (department_id, today)).fetchone()['total']
    expenses = conn.execute("SELECT COALESCE(SUM(CASE WHEN expense_type = 'electricity' THEN amount ELSE 0 END), 0) as electricity, COALESCE(SUM(CASE WHEN expense_type = 'rent' THEN amount ELSE 0 END), 0) as rent, COALESCE(SUM(CASE WHEN expense_type = 'other' THEN amount ELSE 0 END), 0) as other, COALESCE(SUM(amount), 0) as total FROM transactions WHERE department_id = ? AND transaction_type = 'expense' AND transaction_date = ?", (department_id, today)).fetchone()
    conn.close()
    net_profit = income - expenses['total']
    return jsonify({
        'income': float(income),
        'expenses': {key: float(value) for key, value in dict(expenses).items()},
        'net_profit': float(net_profit)
    })

@app.route('/api/comprehensive_report')
@login_required
def get_comprehensive_report():
    today = date.today().isoformat()
    conn = get_db_connection()
    departments = conn.execute('SELECT * FROM departments WHERE is_active = 1 ORDER BY display_order').fetchall()
    departments_data = []
    total_income = 0
    total_expenses = 0
    for dept in departments:
        dept_income = conn.execute("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE department_id = ? AND transaction_type = 'income' AND transaction_date = ?", (dept['id'], today)).fetchone()['total']
        dept_expenses = conn.execute("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE department_id = ? AND transaction_type = 'expense' AND transaction_date = ?", (dept['id'], today)).fetchone()['total']
        departments_data.append({'id': dept['id'], 'name': dept['name'], 'income': float(dept_income), 'expenses': float(dept_expenses), 'profit': float(dept_income - dept_expenses)})
        total_income += dept_income
        total_expenses += dept_expenses
    conn.close()
    return jsonify({'departments': departments_data, 'totals': {'income': float(total_income), 'expenses': float(total_expenses), 'profit': float(total_income - total_expenses)}})

@app.route('/api/daily_report')
@login_required
def get_daily_report():
    report_date = request.args.get('date', date.today().isoformat())
    department_id = request.args.get('department_id', type=int)
    expense_type = request.args.get('expense_type')
    
    conn = get_db_connection()
    
    query = """
        SELECT t.*, d.name as department_name 
        FROM transactions t 
        JOIN departments d ON t.department_id = d.id 
        WHERE d.is_active = 1 AND t.transaction_date = ?
    """
    params = [report_date]
    
    if department_id:
        query += " AND t.department_id = ?"
        params.append(department_id)
    
    if expense_type:
        query += " AND t.expense_type = ?"
        params.append(expense_type)
    
    query += " ORDER BY t.created_at DESC"
    
    transactions = conn.execute(query, params).fetchall()
    
    # إحصائيات إضافية
    stats_query = """
        SELECT 
            COALESCE(SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END), 0) as total_income,
            COALESCE(SUM(CASE WHEN transaction_type = 'expense' THEN amount ELSE 0 END), 0) as total_expense,
            COUNT(*) as transaction_count
        FROM transactions 
        WHERE transaction_date = ?
    """
    stats_params = [report_date]
    
    if department_id:
        stats_query += " AND department_id = ?"
        stats_params.append(department_id)
    
    if expense_type:
        stats_query += " AND expense_type = ?"
        stats_params.append(expense_type)
    
    stats = conn.execute(stats_query, stats_params).fetchone()
    
    conn.close()
    
    return jsonify({
        'transactions': [dict(t) for t in transactions],
        'stats': {
            'total_income': float(stats['total_income']),
            'total_expense': float(stats['total_expense']),
            'transaction_count': stats['transaction_count'],
            'net_profit': float(stats['total_income'] - stats['total_expense'])
        }
    })

# --- API التقرير الشهري المطور ---
@app.route('/api/monthly_report')
@login_required
def get_monthly_report():
    report_month = request.args.get('month', date.today().strftime('%Y-%m'))
    conn = get_db_connection()
    departments = conn.execute('SELECT * FROM departments WHERE is_active = 1 ORDER BY display_order').fetchall()
    
    departments_reports = []
    grand_totals = {'income': 0, 'expenses': {'total': 0, 'electricity': 0, 'rent': 0, 'other': 0}, 'profit': 0}

    for dept in departments:
        income = conn.execute("SELECT COALESCE(SUM(amount), 0) as total FROM transactions WHERE department_id = ? AND transaction_type = 'income' AND strftime('%Y-%m', transaction_date) = ?", (dept['id'], report_month)).fetchone()['total']
        expenses_raw = conn.execute("SELECT COALESCE(SUM(CASE WHEN expense_type = 'electricity' THEN amount ELSE 0 END), 0) as electricity, COALESCE(SUM(CASE WHEN expense_type = 'rent' THEN amount ELSE 0 END), 0) as rent, COALESCE(SUM(CASE WHEN expense_type = 'other' THEN amount ELSE 0 END), 0) as other, COALESCE(SUM(amount), 0) as total FROM transactions WHERE department_id = ? AND transaction_type = 'expense' AND strftime('%Y-%m', transaction_date) = ?", (dept['id'], report_month)).fetchone()
        
        income = float(income)
        expenses = {key: float(value) for key, value in dict(expenses_raw).items()}
        profit = income - expenses['total']
        
        departments_reports.append({'id': dept['id'], 'name': dept['name'], 'income': income, 'expenses': expenses, 'profit': profit})
        
        grand_totals['income'] += income
        grand_totals['expenses']['total'] += expenses['total']
        grand_totals['expenses']['electricity'] += expenses['electricity']
        grand_totals['expenses']['rent'] += expenses['rent']
        grand_totals['expenses']['other'] += expenses['other']
        grand_totals['profit'] += profit

    conn.close()
    return jsonify({'month': report_month, 'departments_reports': departments_reports, 'grand_totals': grand_totals})

@app.route('/api/departments', methods=['POST'])
@login_required
def update_department():
    data = request.get_json()
    conn = get_db_connection()
    conn.execute('UPDATE departments SET name = ? WHERE id = ?', (data['name'], data['id']))
    conn.commit()
    conn.close()
    return jsonify({'success': True, 'message': 'تم تحديث اسم القسم بنجاح'})

@app.route('/api/department/<int:department_id>', methods=['DELETE'])
@login_required
def delete_department(department_id):
    conn = get_db_connection()
    conn.execute('UPDATE departments SET is_active = 0 WHERE id = ?', (department_id,))
    conn.commit()
    conn.close()
    return jsonify({'success': True, 'message': 'تم حذف القسم بنجاح'})

# --- API لإغلاق البرنامج ---
@app.route('/api/shutdown', methods=['POST'])
@login_required
def shutdown():
    print("Server is shutting down...")
    os._exit(0)

def open_browser():
    time.sleep(1.5)
    webbrowser.open('http://127.0.0.1:5000/login')

if __name__ == '__main__':
    if not os.path.exists(DATABASE):
        init_database()
    threading.Thread(target=open_browser).start()
    app.run(debug=True, host='127.0.0.1', port=5000, use_reloader=False)