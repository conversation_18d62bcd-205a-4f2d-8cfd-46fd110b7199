/* إعدادات عامة */
* { margin: 0; padding: 0; box-sizing: border-box; }
body { font-family: 'Cairo', sans-serif; background: #f0f2f5; min-height: 100vh; direction: rtl; color: #333; }
.container { max-width: 1600px; margin: 0 auto; padding: 20px; }

/* الهيدر وشريط التنقل */
.header, .navbar { background: white; border-radius: 12px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); }
.navbar { padding: 10px; }
.header-content, .nav-buttons { display: flex; align-items: center; flex-wrap: wrap; }
.header-content { justify-content: space-between; }
.nav-buttons { gap: 10px; }
.header h1 { color: #1a202c; font-size: 1.8rem; }
.header h1 i { color: #4a55e1; }
.header-info { gap: 25px; font-size: 1rem; color: #4a5568; }
.nav-btn { background: #f7f7f7; color: #2d3748; border: 1px solid #e2e8f0; padding: 10px 18px; border-radius: 8px; cursor: pointer; font-size: 1rem; transition: all 0.2s ease; display: flex; align-items: center; gap: 8px; }
.nav-btn:hover { background: #e2e8f0; }
.nav-btn.active { background: #4a55e1; color: white; border-color: #4a55e1; }
.print-btn { background: #38a169; color: white; border-color: #38a169; margin-right: auto; }
.exit-btn { background: #e53e3e; color: white; border-color: #e53e3e; }
.print-btn:hover { background: #2f855a; }
.exit-btn:hover { background: #c53030; }

/* الأقسام */
.section { display: none; }
.section.active { display: block; }
.departments-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(360px, 1fr)); gap: 20px; }
.department-card { background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); transition: transform 0.2s ease, box-shadow 0.2s ease; overflow: hidden; }
.department-card:hover { transform: translateY(-4px); box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08); }
.department-header { display: flex; justify-content: center; align-items: center; position: relative; padding: 15px; border-bottom: 1px solid #f1f1f1; }
.department-header h3 { color: #1a202c; font-size: 1.2rem; font-weight: 600; }
.delete-btn { position: absolute; top: 50%; left: 15px; transform: translateY(-50%); background: none; border: none; color: #e53e3e; font-size: 1rem; cursor: pointer; padding: 5px; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; }
.delete-btn:hover { color: #c53030; background-color: #fed7d7; }
.department-stats { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; padding: 20px; }
.stat-item { background: #f9fafb; padding: 12px; border-radius: 8px; text-align: center; }
.stat-label { font-size: 0.85rem; color: #718096; margin-bottom: 5px; }
.stat-value { font-size: 1.1rem; font-weight: 600; }
.stat-value.profit { color: #38a169; }
.stat-value.loss { color: #e53e3e; }
.income .stat-value { color: #38a169; }
.expenses .stat-value { color: #e53e3e; }
.department-actions { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; padding: 0 20px 20px; }
.action-btn { padding: 10px; border: none; border-radius: 8px; cursor: pointer; font-size: 0.9rem; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 5px; color: white; }
.income-btn { background: #38a169; }
.expense-btn { background: #e53e3e; }
.income-btn:hover { background: #2f855a; }
.expense-btn:hover { background: #c53030; }

/* التقارير */
.reports-container { background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); }
.reports-container h2 { text-align: center; margin-bottom: 25px; color: #1a202c; }
.summary-cards { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
.summary-card { padding: 20px; border-radius: 10px; text-align: center; color: white; }
.total-income { background: linear-gradient(135deg, #48bb78, #38a169); }
.total-expenses { background: linear-gradient(135deg, #f56565, #e53e3e); }
.net-profit { background: linear-gradient(135deg, #4299e1, #3182ce); }
.summary-card h3 { margin-bottom: 10px; font-size: 1rem; font-weight: 400; }
.summary-card .amount { font-size: 1.8rem; font-weight: 700; }
.date-filter, .month-filter { padding: 15px; border-radius: 10px; margin-bottom: 20px; display: flex; align-items: center; gap: 15px; border: 1px solid #e2e8f0; }
.date-filter input, .month-filter input, .date-filter button, .month-filter button { padding: 8px 12px; border-radius: 8px; border: 1px solid #cbd5e0; font-family: 'Cairo', sans-serif; }
.date-filter button, .month-filter button { background: #4a55e1; color: white; cursor: pointer; }

/* تصميم التقرير الشهري الجديد */
.monthly-reports-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 20px; margin-bottom: 25px; }
.monthly-card, .monthly-card-total { background: #f9fafb; border: 1px solid #e2e8f0; border-radius: 10px; padding: 20px; }
.monthly-card h4, .monthly-card-total h3 { margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #e2e8f0; color: #2d3748; }
.monthly-stat, .monthly-sub-stat { display: flex; justify-content: space-between; align-items: center; padding: 6px 0; }
.monthly-sub-stat { font-size: 0.9em; color: #718096; padding-right: 15px; }
.monthly-stat strong { font-size: 1.1em; }
.monthly-card-total { background: white; border: 1px solid #4a55e1; }
.monthly-card-total h3 { font-size: 1.5em; text-align: center; }
.total-stats .monthly-stat { font-size: 1.2em; }

/* الإعدادات */
.settings-container { max-width: 800px; margin: 0 auto; background: white; padding: 25px; border-radius: 12px; }
.settings-group { margin-bottom: 25px; padding-bottom: 20px; border-bottom: 1px solid #f1f1f1; }
.settings-group:last-of-type { border-bottom: none; }
.settings-group h3 { margin-bottom: 15px; color: #2d3748; display: flex; align-items: center; gap: 10px; }
.add-department-form { display: flex; gap: 10px; }
.add-department-form input { flex: 1; padding: 10px; border: 1px solid #cbd5e0; border-radius: 8px; }
.add-btn, .save-settings-btn { background: #38a169; color: white; padding: 10px 22px; border-radius: 8px; cursor: pointer; font-size: 1rem; border: none; }
.save-settings-btn { display: block; margin: 20px auto 0; }
.department-setting { display: flex; align-items: center; gap: 15px; margin-bottom: 10px; }
#currency-select, .department-name-input { width: 100%; padding: 10px; border: 1px solid #cbd5e0; border-radius: 8px; }

/* رسائل ومكونات عامة */
.goodbye-message { text-align:center; padding-top:50px; font-family: Cairo, sans-serif; color: #4a5568; }
.no-data { text-align: center; padding: 30px; color: #718096; background: #f9fafb; border-radius: 10px; }
.notification { position: fixed; bottom: 20px; left: 20px; color: white; padding: 12px 20px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 1001; font-size: 0.95rem; }
.notification.success { background-color: #38a169; }
.notification.error { background-color: #e53e3e; }
.notification.info { background-color: #3182ce; }

/* نافذة المعاملات */
.modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.4); backdrop-filter: blur(4px); }
.modal-content { background-color: white; margin: 10% auto; padding: 30px; border-radius: 15px; width: 90%; max-width: 450px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); position: relative; }
.close { color: #aaa; position: absolute; top: 10px; left: 15px; font-size: 28px; font-weight: bold; cursor: pointer; }
.form-group { margin-bottom: 18px; }
.form-group label { display: block; margin-bottom: 6px; color: #2d3748; font-weight: 600; }
.form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #cbd5e0; border-radius: 8px; }
.form-buttons { display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px; }
.form-buttons button { padding: 10px 22px; border-radius: 8px; cursor: pointer; border: none; }
.form-buttons button[type="submit"] { background: #4a55e1; color: white; }
.form-buttons button[type="button"] { background: #e2e8f0; color: #2d3748; }

/* تصميم التقرير اليومي المفصل */
.daily-report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: #f9fafb;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.stat-card h4 {
    margin-bottom: 10px;
    color: #4a5568;
    font-size: 0.9rem;
}

.stat-card .stat-value {
    font-size: 1.3rem;
    font-weight: 700;
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.transactions-table th,
.transactions-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
}

.transactions-table th {
    background: #f7fafc;
    font-weight: 600;
    color: #2d3748;
}

.transactions-table tr:hover {
    background: #f9fafb;
}

.transactions-table .income {
    color: #38a169;
    font-weight: 600;
}

.transactions-table .expense {
    color: #e53e3e;
    font-weight: 600;
}

.date-filter, .month-filter {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    border: 1px solid #e2e8f0;
}

.date-filter label, .month-filter label {
    margin: 0;
    font-weight: 600;
    color: #4a5568;
}

.date-filter input, 
.date-filter select,
.month-filter input, 
.month-filter select {
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #cbd5e0;
    font-family: 'Cairo', sans-serif;
}

.date-filter button, .month-filter button {
    background: #4a55e1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
}
/* تحسينات إضافية للتواريخ */
.header-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

#current-date {
    font-weight: 600;
    color: #2d3748;
}

/* تحسينات للتقارير اليومية */
.daily-report-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
}

.stat-card h4 {
    margin-bottom: 10px;
    color: #4a5568;
    font-size: 0.9rem;
    font-weight: 600;
}

.stat-card .stat-value {
    font-size: 1.4rem;
    font-weight: 700;
}

.stat-card .stat-value.income {
    color: #38a169;
}

.stat-card .stat-value.expense {
    color: #e53e3e;
}

.stat-card .stat-value.profit {
    color: #38a169;
}

.stat-card .stat-value.loss {
    color: #e53e3e;
}

/* تحسينات الجداول */
.transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.transactions-table th,
.transactions-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
}

.transactions-table th {
    background: #f7fafc;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
}

.transactions-table tr:hover {
    background: #f9fafb;
}

.transactions-table .income {
    color: #38a169;
    font-weight: 600;
}

.transactions-table .expense {
    color: #e53e3e;
    font-weight: 600;
}

/* تحسينات الفلاتر */
.date-filter, .month-filter {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 25px;
    border: 1px solid #e2e8f0;
    background: #f9fafb;
}

.date-filter label, .month-filter label {
    margin: 0;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
}

.date-filter input, 
.date-filter select,
.month-filter input, 
.month-filter select {
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid #cbd5e0;
    font-family: 'Cairo', sans-serif;
    min-width: 150px;
}

.date-filter button, .month-filter button {
    background: #4a55e1;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: all 0.2s ease;
}

.date-filter button:hover, .month-filter button:hover {
    background: #3c46b5;
    transform: translateY(-1px);
}