// متغيرات عامة
let departments = [];
let settings = {};

document.addEventListener('DOMContentLoaded', () => {
    initializeApp();
    setupEventListeners();
    updateCurrentDate();
    setInterval(updateCurrentDate, 60000);
});

async function initializeApp() {
    try {
        await Promise.all([loadSettings(), loadDepartments()]);
        renderUI();
        setDefaultDates();
    } catch (error) {
        console.error('Error initializing app:', error);
    }
}

function setupEventListeners() {
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.classList.contains('print-btn') && !this.classList.contains('exit-btn')) {
                switchSection(this.dataset.section);
            }
        });
    });
    document.getElementById('transaction-form').addEventListener('submit', handleTransactionSubmit);
}

function renderUI() {
    const activeSectionId = document.querySelector('.section.active')?.id || 'departments-section';
    renderDepartments();
    switch (activeSectionId) {
        case 'reports-section': loadComprehensiveReport(); break;
        case 'daily-reports-section': 
            if (!document.getElementById('daily-filters')) {
                buildDailyReportFilters();
            }
            loadDailyReport(); 
            break;
        case 'monthly-reports-section': loadMonthlyReport(); break;
        case 'settings-section': renderDepartmentSettings(); break;
    }
}

function updateCurrentDate() {
    const now = new Date();
    
    // التاريخ الميلادي باللغة العربية
    const gregorianOptions = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        calendar: 'gregory'
    };
    const gregorianDate = now.toLocaleDateString('ar-SA', gregorianOptions);
    
    document.getElementById('current-date').textContent = gregorianDate;
}

async function loadSettings() {
    const response = await fetch('/api/settings');
    settings = await response.json();
    document.getElementById('current-currency').textContent = settings.currency || 'العملة';
    const currencySelect = document.getElementById('currency-select');
    if (currencySelect) currencySelect.value = settings.currency || 'ريال يمني';
}

async function loadDepartments() {
    const response = await fetch('/api/departments');
    departments = await response.json();
}

function renderDepartments() {
    const grid = document.getElementById('departments-grid');
    grid.innerHTML = '';
    if (departments.length > 0) {
        departments.forEach(dept => {
            grid.appendChild(createDepartmentCard(dept));
            loadDepartmentSummary(dept.id);
        });
    } else {
        grid.innerHTML = '<p class="no-data">لا توجد أقسام لعرضها. يمكنك إضافة قسم جديد من صفحة الإعدادات.</p>';
    }
}

function createDepartmentCard(department) {
    const card = document.createElement('div');
    card.className = 'department-card';
    card.innerHTML = `
        <div class="department-header">
            <h3>${department.name}</h3>
            <button class="delete-btn" onclick="deleteDepartment(event, ${department.id}, '${department.name}')" title="حذف القسم"><i class="fas fa-trash-alt"></i></button>
        </div>
        <div class="department-stats">
            <div class="stat-item income"><div class="stat-label">إيرادات اليوم</div><div class="stat-value" id="income-${department.id}">0</div></div>
            <div class="stat-item expenses"><div class="stat-label">مصروفات اليوم</div><div class="stat-value" id="expenses-${department.id}">0</div></div>
            <div class="stat-item"><div class="stat-label">كهرباء</div><div class="stat-value" id="electricity-${department.id}">0</div></div>
            <div class="stat-item"><div class="stat-label">إيجار</div><div class="stat-value" id="rent-${department.id}">0</div></div>
            <div class="stat-item"><div class="stat-label">أخرى</div><div class="stat-value" id="other-${department.id}">0</div></div>
            <div class="stat-item net-profit"><div class="stat-label">صافي الربح</div><div class="stat-value" id="profit-${department.id}">0</div></div>
        </div>
        <div class="department-actions">
            <button class="action-btn income-btn" onclick="openTransactionModal(${department.id}, 'income')"><i class="fas fa-plus"></i> إضافة إيراد</button>
            <button class="action-btn expense-btn" onclick="openTransactionModal(${department.id}, 'expense')"><i class="fas fa-minus"></i> إضافة مصروف</button>
        </div>`;
    return card;
}

async function loadDepartmentSummary(departmentId) {
    try {
        const response = await fetch(`/api/daily_summary/${departmentId}`);
        const summary = await response.json();
        document.getElementById(`income-${departmentId}`).textContent = formatCurrency(summary.income);
        document.getElementById(`expenses-${departmentId}`).textContent = formatCurrency(summary.expenses.total);
        document.getElementById(`electricity-${departmentId}`).textContent = formatCurrency(summary.expenses.electricity);
        document.getElementById(`rent-${departmentId}`).textContent = formatCurrency(summary.expenses.rent);
        document.getElementById(`other-${departmentId}`).textContent = formatCurrency(summary.expenses.other);
        const profitEl = document.getElementById(`profit-${departmentId}`);
        profitEl.textContent = formatCurrency(summary.net_profit);
        profitEl.className = 'stat-value'; // Reset class
        if (summary.net_profit > 0) profitEl.classList.add('profit'); else if (summary.net_profit < 0) profitEl.classList.add('loss');
    } catch (error) { /* Silently fail for summaries */ }
}

async function handleTransactionSubmit(e) {
    e.preventDefault();
    const amount = parseFloat(document.getElementById('amount').value);
    if (!amount || amount <= 0) {
        showNotification('الرجاء إدخال مبلغ صحيح وموجب', 'error');
        return;
    }
    const formData = {
        department_id: parseInt(document.getElementById('department-id').value),
        transaction_type: document.getElementById('transaction-type').value,
        amount,
        description: document.getElementById('description').value,
        transaction_date: new Date().toISOString().split('T')[0],
        expense_type: document.getElementById('transaction-type').value === 'expense' ? document.getElementById('expense-type').value : null
    };
    
    try {
        const response = await fetch('/api/transactions', { method: 'POST', headers: {'Content-Type': 'application/json'}, body: JSON.stringify(formData) });
        if (!response.ok) throw new Error('فشل في إضافة المعاملة');
        showNotification('تم إضافة المعاملة بنجاح', 'success');
        closeTransactionModal();
        await loadDepartmentSummary(formData.department_id);
        if (document.querySelector('#reports-section.active')) await loadComprehensiveReport();
        if (document.querySelector('#daily-reports-section.active')) await loadDailyReport();
        if (document.querySelector('#monthly-reports-section.active')) await loadMonthlyReport();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

function switchSection(sectionName) {
    document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
    document.getElementById(`${sectionName}-section`).classList.add('active');
    document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
    document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
    renderUI();
}

// --- دالة التقرير الشهري المحدثة ---
async function loadMonthlyReport() {
    const selectedMonth = document.getElementById('monthly-date').value;
    if (!selectedMonth) return;
    try {
        const response = await fetch(`/api/monthly_report?month=${selectedMonth}`);
        const data = await response.json();
        const container = document.getElementById('monthly-report-content');
        container.innerHTML = '';

        if (data.departments_reports.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد معاملات في هذا الشهر</p>';
            return;
        }

        const reportsGrid = document.createElement('div');
        reportsGrid.className = 'monthly-reports-grid';
        
        data.departments_reports.forEach(report => {
            const card = document.createElement('div');
            card.className = 'monthly-card';
            card.innerHTML = `
                <h4>${report.name}</h4>
                <div class="monthly-stat income"><span>الإيرادات</span><strong>${formatCurrency(report.income)}</strong></div>
                <div class="monthly-stat expenses"><span>المصروفات</span><strong>${formatCurrency(report.expenses.total)}</strong></div>
                <div class="monthly-sub-stat"><span>- كهرباء</span><strong>${formatCurrency(report.expenses.electricity)}</strong></div>
                <div class="monthly-sub-stat"><span>- إيجار</span><strong>${formatCurrency(report.expenses.rent)}</strong></div>
                <div class="monthly-sub-stat"><span>- أخرى</span><strong>${formatCurrency(report.expenses.other)}</strong></div>
                <div class="monthly-stat profit"><span>صافي الربح</span><strong class="${report.profit >= 0 ? 'profit' : 'loss'}">${formatCurrency(report.profit)}</strong></div>
            `;
            reportsGrid.appendChild(card);
        });
        container.appendChild(reportsGrid);

        const totals = data.grand_totals;
        const totalCard = document.createElement('div');
        totalCard.className = 'monthly-card-total';
        totalCard.innerHTML = `
            <h3><i class="fas fa-globe-asia"></i> التقرير الشامل لشهر ${selectedMonth}</h3>
            <div class="total-stats">
                <div class="monthly-stat income"><span>إجمالي الإيرادات</span><strong>${formatCurrency(totals.income)}</strong></div>
                <div class="monthly-stat expenses"><span>إجمالي المصروفات</span><strong>${formatCurrency(totals.expenses.total)}</strong></div>
                <div class="monthly-stat profit"><span>إجمالي صافي الربح</span><strong class="${totals.profit >= 0 ? 'profit' : 'loss'}">${formatCurrency(totals.profit)}</strong></div>
            </div>
        `;
        container.appendChild(totalCard);

    } catch (error) {
        showNotification('فشل تحميل التقرير الشهري', 'error');
    }
}

// --- وظائف الإعدادات الجديدة ---
function renderDepartmentSettings() {
    const container = document.getElementById('department-names-settings');
    container.innerHTML = '';
    departments.forEach(dept => {
        const settingDiv = document.createElement('div');
        settingDiv.className = 'department-setting';
        settingDiv.innerHTML = `<label for="dept-${dept.id}">${dept.name}:</label><input type="text" class="department-name-input" data-dept-id="${dept.id}" value="${dept.name}" onchange="this.dataset.changed='true'">`;
        container.appendChild(settingDiv);
    });
}

async function addNewDepartment() {
    const input = document.getElementById('new-department-name');
    const name = input.value.trim();
    if (!name) {
        showNotification('الرجاء إدخال اسم للقسم الجديد', 'error');
        return;
    }
    try {
        const response = await fetch('/api/department', { method: 'POST', headers: {'Content-Type': 'application/json'}, body: JSON.stringify({ name }) });
        const result = await response.json();
        if (!response.ok) throw new Error(result.message);
        showNotification('تم إضافة القسم بنجاح', 'success');
        input.value = '';
        await loadDepartments();
        renderDepartmentSettings();
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

async function saveSettings() {
    try {
        const currencyPayload = { currency: document.getElementById('currency-select').value };
        await fetch('/api/settings', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(currencyPayload) });
        
        const deptUpdates = Array.from(document.querySelectorAll('.department-name-input[data-changed="true"]'))
            .map(input => fetch('/api/departments', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ id: parseInt(input.dataset.deptId), name: input.value }) }));
        
        await Promise.all(deptUpdates);
        showNotification('تم حفظ الإعدادات بنجاح', 'success');
        await initializeApp();
    } catch (error) {
        showNotification('فشل في حفظ الإعدادات', 'error');
    }
}

// --- وظيفة الخروج الجديدة ---
async function exitApplication() {
    if (confirm('هل أنت متأكد من رغبتك في إغلاق البرنامج؟')) {
        try {
            await fetch('/logout');
            window.location.href = '/login';
        } catch (error) { 
            window.location.href = '/login';
        }
    }
}

// --- وظائف التقارير اليومية المحسنة ---
function buildDailyReportFilters() {
    const container = document.querySelector('#daily-reports-section .reports-container');
    const existingFilters = document.getElementById('daily-filters');
    if (existingFilters) return;
    
    const filtersHtml = `
        <div class="date-filter" id="daily-filters">
            <label for="daily-date">اختر التاريخ:</label>
            <input type="date" id="daily-date">
            
            <label for="daily-department-filter">القسم:</label>
            <select id="daily-department-filter">
                <option value="">جميع الأقسام</option>
                ${departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('')}
            </select>
            
            <label for="daily-expense-type-filter">نوع المصروف:</label>
            <select id="daily-expense-type-filter">
                <option value="">جميع الأنواع</option>
                <option value="electricity">كهرباء</option>
                <option value="rent">إيجار</option>
                <option value="other">أخرى</option>
            </select>
            
            <button onclick="loadDailyReport()">عرض التقرير</button>
        </div>
    `;
    
    const dateFilter = container.querySelector('.date-filter');
    if (dateFilter) {
        dateFilter.outerHTML = filtersHtml;
    }
}

async function loadDailyReport() {
    const selectedDate = document.getElementById('daily-date').value;
    const departmentFilter = document.getElementById('daily-department-filter')?.value || '';
    const expenseTypeFilter = document.getElementById('daily-expense-type-filter')?.value || '';
    
    if (!selectedDate) return;
    
    let url = `/api/daily_report?date=${selectedDate}`;
    if (departmentFilter) url += `&department_id=${departmentFilter}`;
    if (expenseTypeFilter) url += `&expense_type=${expenseTypeFilter}`;
    
    try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('فشل تحميل التقرير');
        
        const data = await response.json();
        const container = document.getElementById('daily-report-content');
        
        if (data.transactions.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد معاملات في هذا التاريخ</p>';
            return;
        }
        
        // بناء واجهة التقرير مع الإحصائيات
        let html = `
            <div class="daily-report-stats">
                <div class="stat-card">
                    <h4>إجمالي الإيرادات</h4>
                    <div class="stat-value income">${formatCurrency(data.stats.total_income)}</div>
                </div>
                <div class="stat-card">
                    <h4>إجمالي المصروفات</h4>
                    <div class="stat-value expense">${formatCurrency(data.stats.total_expense)}</div>
                </div>
                <div class="stat-card">
                    <h4>صافي الربح</h4>
                    <div class="stat-value ${data.stats.net_profit >= 0 ? 'profit' : 'loss'}">${formatCurrency(data.stats.net_profit)}</div>
                </div>
                <div class="stat-card">
                    <h4>عدد المعاملات</h4>
                    <div class="stat-value">${data.stats.transaction_count}</div>
                </div>
            </div>
            <table class="transactions-table">
                <thead>
                    <tr>
                        <th>القسم</th>
                        <th>النوع</th>
                        <th>المبلغ</th>
                        <th>الوصف</th>
                        <th>نوع المصروف</th>
                        <th>الوقت</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        data.transactions.forEach(t => {
            const expenseTypeText = t.expense_type === 'electricity' ? 'كهرباء' : 
                                  t.expense_type === 'rent' ? 'إيجار' : 
                                  t.expense_type === 'other' ? 'أخرى' : '-';
            
            html += `
                <tr>
                    <td>${t.department_name}</td>
                    <td class="${t.transaction_type}">${t.transaction_type === 'income' ? 'إيراد' : 'مصروف'}</td>
                    <td class="${t.transaction_type}">${formatCurrency(t.amount)}</td>
                    <td>${t.description || '-'}</td>
                    <td>${expenseTypeText}</td>
                    <td>${new Date(t.created_at).toLocaleTimeString('ar-SA')}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        container.innerHTML = html;
        
    } catch (error) { 
        showNotification('فشل تحميل التقرير اليومي', 'error');
    }
}

// --- وظائف مساعدة ---
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(amount || 0);
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
}

function setDefaultDates() {
    document.getElementById('daily-date').valueAsDate = new Date();
    document.getElementById('monthly-date').value = new Date().toISOString().slice(0, 7);
}

function openTransactionModal(departmentId, transactionType) {
    const deptName = departments.find(d => d.id === departmentId)?.name || '';
    document.getElementById('modal-title').textContent = `${transactionType === 'income' ? 'إضافة إيراد' : 'إضافة مصروف'} - ${deptName}`;
    document.getElementById('department-id').value = departmentId;
    document.getElementById('transaction-type').value = transactionType;
    document.getElementById('expense-type-group').style.display = transactionType === 'expense' ? 'block' : 'none';
    document.getElementById('transaction-form').reset();
    document.getElementById('transaction-modal').style.display = 'block';
    document.getElementById('amount').focus();
}

function closeTransactionModal() {
    document.getElementById('transaction-modal').style.display = 'none';
}

async function deleteDepartment(event, departmentId, departmentName) {
    event.stopPropagation();
    if (confirm(`هل أنت متأكد من حذف قسم "${departmentName}"؟`)) {
        try {
            const response = await fetch(`/api/department/${departmentId}`, { method: 'DELETE' });
            if (!response.ok) throw new Error('فشل الحذف');
            showNotification('تم حذف القسم بنجاح', 'success');
            await initializeApp();
        } catch (error) {
            showNotification(error.message, 'error');
        }
    }
}

async function loadComprehensiveReport() {
    try {
        const response = await fetch('/api/comprehensive_report');
        const data = await response.json();
        document.getElementById('total-income').textContent = formatCurrency(data.totals.income);
        document.getElementById('total-expenses').textContent = formatCurrency(data.totals.expenses);
        document.getElementById('net-profit').textContent = formatCurrency(data.totals.profit);
        const summaryContainer = document.getElementById('departments-summary');
        summaryContainer.innerHTML = '';
        if (data.departments.length > 0) {
            data.departments.forEach(dept => {
                const deptCard = document.createElement('div');
                deptCard.className = 'summary-card';
                deptCard.innerHTML = `<h4>${dept.name}</h4><div class="dept-summary-stats"><div class="stat"><span>الإيرادات:</span><span class="value income">${formatCurrency(dept.income)}</span></div><div class="stat"><span>المصروفات:</span><span class="value expenses">${formatCurrency(dept.expenses)}</span></div><div class="stat"><span>صافي الربح:</span><span class="value ${dept.profit >= 0 ? 'profit' : 'loss'}">${formatCurrency(dept.profit)}</span></div></div>`;
                summaryContainer.appendChild(deptCard);
            });
        } else {
            summaryContainer.innerHTML = '<p class="no-data">لا توجد بيانات لعرضها لليوم الحالي.</p>';
        }
    } catch (error) { showNotification('فشل تحميل التقرير الشامل', 'error'); }
}

function printReport(){ window.print(); }
window.onclick = (event) => { if (event.target == document.getElementById('transaction-modal')) closeTransactionModal(); };